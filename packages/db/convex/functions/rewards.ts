import { mutation, query } from '../_generated/server';
import { v } from 'convex/values';
import { getUserByClerkId } from './users';

/**
 * READ-ONLY query to get the rewards catalog for the current user's client.
 */
export const getRewardsCatalog = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Unauthenticated call to getRewardsCatalog');
    }

    const user = await getUserByClerkId(ctx, identity.subject);
    if (!user || !user.clientId) {
      throw new Error('User or user client ID not found.');
    }

    const clientConfig = await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .unique();

    if (!clientConfig) {
      console.error(
        `No client configuration found for clientId: ${user.clientId}`
      );
      return [];
    }

    return clientConfig.rewards;
  },
});

/**
 * READ-ONLY query to get the current user's redemption history.
 */
export const getRedemptionHistory = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Unauthenticated call to getRedemptionHistory');
    }

    const user = await getUserByClerkId(ctx, identity.subject);
    if (!user) {
      throw new Error('User not found.');
    }

    return await ctx.db
      .query('userRedemptions')
      .withIndex('by_user_id', (q) => q.eq('userId', user._id))
      .order('desc')
      .collect();
  },
});

/**
 * MUTATION to redeem a reward. This is an atomic transaction.
 */
export const redeemReward = mutation({
  args: {
    rewardId: v.string(),
  },
  handler: async (ctx, { rewardId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Unauthenticated call to redeemReward');
    }

    const user = await getUserByClerkId(ctx, identity.subject);
    if (!user || !user.clientId) {
      throw new Error('User or user client ID not found.');
    }

    const clientConfig = await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .unique();

    if (!clientConfig || !clientConfig.rewards) {
      throw new Error('Could not find client configuration or rewards.');
    }

    const rewardToRedeem = clientConfig.rewards.find((r) => r.id === rewardId);

    if (!rewardToRedeem) {
      throw new Error(`Reward with ID "${rewardId}" not found.`);
    }

    if (user.points < rewardToRedeem.cost) {
      throw new Error('Insufficient points to redeem this reward.');
    }

    // Atomically update user points and create redemption record
    const newPoints = user.points - rewardToRedeem.cost;
    await ctx.db.patch(user._id, { points: newPoints });

    await ctx.db.insert('userRedemptions', {
      userId: user._id,
      clientId: user.clientId,
      rewardId: rewardToRedeem.id,
      rewardName: rewardToRedeem.name,
      pointsSpent: rewardToRedeem.cost,
      redemptionTimestamp: Date.now(),
    });

    return { success: true, newPoints };
  },
});
