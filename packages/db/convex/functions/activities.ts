import { query, mutation } from '../_generated/server';
import { getUserByClerkId } from './users';
import { evaluateUserMilestones } from '../lib/MilestoneService';
import { evaluateAndUpdateUserTier } from '../lib/TierService';
import { Doc, Id } from '../_generated/dataModel';

/**
 * Gets recent activities for the authenticated user
 */
export const getRecentActivities = query({
  args: {},
  handler: async (ctx) => {
    console.log('📋 getRecentActivities called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      console.log('🔐 Auth identity:', {
        hasIdentity: !!identity,
        subject: identity?.subject,
        email: identity?.email,
      });

      if (!identity) {
        console.error('❌ No identity found in getRecentActivities');
        throw new Error('Authentication required');
      }

      const clerkUserId = identity.subject;
      const user = await getUserByClerkId(ctx, clerkUserId);
      console.log('👤 User lookup result:', {
        hasUser: !!user,
        userId: user?._id,
        clerkUserId,
      });

      if (!user) {
        console.error('❌ User not found in getRecentActivities');
        throw new Error('User not found');
      }

      // Get the user's recent activities, sorted by timestamp (newest first)
      const activities = await ctx.db
        .query('activities')
        .withIndex('by_user_id', (q) => q.eq('userId', user._id))
        .order('desc')
        .take(10);

      console.log('🎯 Activities found:', activities.length);
      return activities;
    } catch (error) {
      console.error('💥 Error in getRecentActivities:', error);
      throw error;
    }
  },
});

/**
 * Logs class attendance activity, updates progress, and evaluates milestones.
 */
export const logClassAttendance = mutation({
  args: {},
  handler: async (ctx) => {
    // 1. Get authenticated user
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error('Authentication required');
    const user = await getUserByClerkId(ctx, identity.subject);
    if (!user || !user.clientId) throw new Error('User or client not found');

    const activityTypeKey = 'class_attendance';

    // 2. Record the raw activity
    const activityId = await ctx.db.insert('activities', {
      userId: user._id,
      clientId: user.clientId,
      activityType: activityTypeKey,
      timestamp: Date.now(),
      metadata: {},
    });

    // 3. Update user's activity count for this type
    const userActivityCount = await ctx.db
      .query('userActivityCounts')
      .withIndex('by_user_and_activity', (q) =>
        q.eq('userId', user._id).eq('activityTypeKey', activityTypeKey)
      )
      .unique();

    const newCount = (userActivityCount?.count || 0) + 1;

    if (userActivityCount) {
      await ctx.db.patch(userActivityCount._id, { count: newCount });
    } else {
      await ctx.db.insert('userActivityCounts', {
        userId: user._id,
        activityTypeKey: activityTypeKey,
        count: newCount,
      });
    }

    // 4. Evaluate milestones based on the new count
    const activeMilestones = await ctx.db
      .query('milestones')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('isEnabled'), true))
      .collect();

    const alreadyAchieved = await ctx.db
      .query('userMilestoneProgress')
      .withIndex('by_user_id', (q) => q.eq('userId', user._id))
      .collect();
    const achievedIds = new Set(alreadyAchieved.map((a) => a.milestoneId));

    const newlyAchieved: Doc<'milestones'>[] = [];
    let milestonePointsAwarded = 0;

    for (const milestone of activeMilestones) {
      if (
        milestone.conditions.activityTypeMatcher === activityTypeKey &&
        newCount >= milestone.conditions.countThreshold
      ) {
        const hasAchieved = achievedIds.has(milestone._id);
        if (!hasAchieved || milestone.isRepeatable) {
          // TODO: Add logic for repeatable milestones (resetting progress)
          if (!hasAchieved) {
            newlyAchieved.push(milestone);
            const points =
              (milestone.rewards.find((r) => r.type === 'points')
                ?.value as number) || 0;
            milestonePointsAwarded += points;
            await ctx.db.insert('userMilestoneProgress', {
              userId: user._id,
              milestoneId: milestone._id,
              achievedAt: Date.now(),
              pointsEarned: points,
              badgesEarned: [], // Placeholder for badge logic
            });
          }
        }
      }
    }

    // 5. Update user points and evaluate tier
    const baseActivityPoints = 10;
    const totalPointsEarned = baseActivityPoints + milestonePointsAwarded;
    const newUserPoints = user.points + totalPointsEarned;
    await ctx.db.patch(user._id, { points: newUserPoints });

    const tierEvaluation = await evaluateAndUpdateUserTier(
      ctx,
      user._id,
      newUserPoints,
      user.tier,
      user.clientId!
    );

    // 6. Return comprehensive response
    return {
      success: true,
      totalPoints: totalPointsEarned,
      newMilestones: newlyAchieved.map((m) => ({
        name: m.name,
        description: m.description,
      })),
      activityId,
      tierAdvancement: tierEvaluation.hasAdvanced
        ? {
            previousTier: tierEvaluation.previousTier,
            newTier: tierEvaluation.newTier,
          }
        : null,
    };
  },
});
