import {
  query,
  QueryCtx,
  internalMutation,
  mutation,
} from '../_generated/server';
import { v } from 'convex/values';
import {
  getUserTierHistory,
  getTierDistribution,
  getUserTierPercentile,
  calculateUserTier,
  TIER_THRESHOLDS,
} from '../lib/TierService';
import { createLogger } from '../lib/logger';
import {
  requireAuthenticatedUser,
  getCurrentAuthenticatedUser,
} from '../lib/authHelpers';

/**
 * Helper function to get a user by their Clerk ID.
 * @param {QueryCtx} ctx - The query context
 * @param {string} clerkUserId - The Clerk user ID to search for
 * @returns {Promise<Doc<'users'> | null>} The user document or null if not found
 */
export async function getUserByClerkId(ctx: QueryCtx, clerkUserId: string) {
  return await ctx.db
    .query('users')
    .withIndex('by_clerk_user_id', (q) => q.eq('clerkUserId', clerkUserId))
    .unique();
}

/**
 * READ-ONLY query to get the current authenticated user.
 * @returns {Promise<Doc<'users'> | null>} The current user or null if not authenticated/found
 */
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const logger = createLogger('getCurrentUser');

    try {
      const user = await getCurrentAuthenticatedUser(ctx);

      if (user) {
        logger.debug('User found', { userId: user._id });
      } else {
        logger.debug('No authenticated user found');
      }

      return user;
    } catch (error) {
      logger.error('Failed to get current user', error as Error);
      return null;
    }
  },
});

/**
 * Gets or creates a user in a single, atomic operation.
 * This is the primary entry point for the client to get user data.
 * @returns {Promise<Doc<'users'>>} The user document
 */
export const getOrCreateUser = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();

      if (!identity) {
        throw new Error('Unauthenticated. Please sign in.');
      }

      const user = await getUserByClerkId(ctx, identity.subject);

      if (user !== null) {
        return user;
      }

      const handleBarClient = await ctx.db
        .query('clients')
        .withIndex('by_slug', (q) => q.eq('slug', 'the-handle-bar'))
        .unique();

      if (!handleBarClient) {
        throw new Error('Default client not found. Please seed the database.');
      }

      const newUser = {
        clerkUserId: identity.subject,
        email: identity.email!,
        firstName: identity.givenName,
        lastName: identity.familyName,
        points: 0,
        tier: 'Bronze',
        clientId: handleBarClient._id,
        role: 'user',
        searchText:
          `${identity.givenName ?? ''} ${identity.familyName ?? ''} ${identity.email!}`.toLowerCase(),
      };

      const newUserId = await ctx.db.insert('users', newUser);
      const createdUser = await ctx.db.get(newUserId);

      return createdUser;
    } catch (error) {
      console.error('Error in getOrCreateUser:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get a user's milestone progress.
 * This can only be called when we know the user exists.
 * @returns {Promise<Doc<'userMilestoneProgress'>[]>} Array of user milestone progress records
 */
export const getUserMilestones = query({
  args: {},
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();

      if (!identity) {
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);

      if (!user) {
        return [];
      }

      const milestones = await ctx.db
        .query('userMilestoneProgress')
        .withIndex('by_user_id', (q) => q.eq('userId', user._id))
        .collect();

      return milestones;
    } catch (error) {
      console.error('Error in getUserMilestones:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get user's tier progress and information.
 * @returns {Promise<Object | null>} Tier progress data or null if user not found
 */
export const getUserTierProgress = query({
  args: {},
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      if (!user) {
        return null;
      }

      const tierProgress = calculateUserTier({
        currentPoints: user.points,
        currentTier: user.tier,
      });

      const tierHistory = await getUserTierHistory(ctx, user._id);

      const userPercentile = await getUserTierPercentile(
        ctx,
        user.tier,
        user.clientId || undefined
      );

      return {
        currentTier: user.tier,
        points: user.points,
        tierProgress,
        tierHistory,
        userPercentile,
        tierThresholds: TIER_THRESHOLDS,
      };
    } catch (error) {
      console.error('Error in getUserTierProgress:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get tier distribution statistics
 */
export const getTierStatistics = query({
  args: {},
  handler: async (ctx) => {
    console.log('📊 getTierStatistics called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.error('❌ No identity found in getTierStatistics');
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      if (!user) {
        console.warn('⚠️ User not found in getTierStatistics');
        return null;
      }

      // Get tier distribution for user's client
      const distribution = await getTierDistribution(
        ctx,
        user.clientId || undefined
      );

      console.log('📈 Tier distribution calculated:', {
        totalUsers: distribution.totalUsers,
        tierCounts: distribution.tierDistribution,
      });

      return {
        totalUsers: distribution.totalUsers,
        tierDistribution: distribution.tierDistribution,
        tierThresholds: TIER_THRESHOLDS,
      };
    } catch (error) {
      console.error('💥 Error in getTierStatistics:', error);
      throw error;
    }
  },
});

/**
 * Internal mutation to create or update a user from webhook
 */
export const internalStoreOrUpdateUser = internalMutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getUserByClerkId(ctx, args.clerkUserId);
    if (user) {
      await ctx.db.patch(user._id, {
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        searchText:
          `${args.firstName ?? ''} ${args.lastName ?? ''} ${args.email}`.toLowerCase(),
      });
    }
    // Note: We no longer create the user here, getOrCreateUser handles that.
  },
});

/**
 * Internal mutation to set a user's role from a webhook.
 */
export const internalSetUserRole = internalMutation({
  args: { clerkUserId: v.string(), role: v.string() },
  handler: async (ctx, { clerkUserId, role }) => {
    const user = await getUserByClerkId(ctx, clerkUserId);

    if (user) {
      await ctx.db.patch(user._id, { role });
      console.log(`✅ Updated role for user ${clerkUserId} to "${role}"`);
    } else {
      console.warn(
        `⚠️ Webhook tried to set role for non-existent user: ${clerkUserId}`
      );
    }
  },
});
