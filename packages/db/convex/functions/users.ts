import {
  query,
  QueryCtx,
  internalMutation,
  mutation,
} from '../_generated/server';
import { v } from 'convex/values';
import {
  getUserTierHistory,
  getTierDistribution,
  getUserTierPercentile,
  calculateUserTier,
  TIER_THRESHOLDS,
} from '../lib/TierService';

/**
 * Helper function to get a user by their Clerk ID
 */
export async function getUserByClerkId(ctx: QueryCtx, clerkUserId: string) {
  return await ctx.db
    .query('users')
    .withIndex('by_clerk_user_id', (q) => q.eq('clerkUserId', clerkUserId))
    .unique();
}

/**
 * READ-ONLY query to get the current authenticated user
 */
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    console.log('👤 getCurrentUser called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      console.log('🔐 Auth identity:', {
        hasIdentity: !!identity,
        subject: identity?.subject,
        email: identity?.email,
      });

      if (!identity) {
        console.error('❌ No identity found in getCurrentUser');
        return null;
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      console.log('👤 User lookup result:', {
        hasUser: !!user,
        userId: user?._id,
        clerkUserId: identity.subject,
      });

      return user;
    } catch (error) {
      console.error('💥 Error in getCurrentUser:', error);
      return null;
    }
  },
});

/**
 * Gets or creates a user in a single, atomic operation.
 * This is the primary entry point for the client to get user data.
 */
export const getOrCreateUser = mutation({
  args: {},
  handler: async (ctx) => {
    console.log('👤 getOrCreateUser called');

    try {
      // 1. Get the authenticated user's identity
      const identity = await ctx.auth.getUserIdentity();
      console.log('🔐 Auth identity:', {
        hasIdentity: !!identity,
        subject: identity?.subject,
        email: identity?.email,
      });

      if (!identity) {
        console.error('❌ No identity found in getOrCreateUser');
        throw new Error('Unauthenticated. Please sign in.');
      }

      // 2. Check if the user already exists in our database
      const user = await getUserByClerkId(ctx, identity.subject);
      console.log('👤 Existing user lookup:', {
        hasUser: !!user,
        userId: user?._id,
        clerkUserId: identity.subject,
      });

      // 3. If the user already exists, return them immediately
      if (user !== null) {
        console.log('✅ Returning existing user');
        return user;
      }

      console.log('🆕 Creating new user...');

      // 4. If they don't exist, create them now
      // (We'll associate them with 'The Handle Bar' client by default)
      const handleBarClient = await ctx.db
        .query('clients')
        .withIndex('by_slug', (q) => q.eq('slug', 'the-handle-bar'))
        .unique();

      if (!handleBarClient) {
        console.error('❌ Default client not found');
        // This should not happen if the database has been seeded
        throw new Error('Default client not found. Please seed the database.');
      }

      console.log('🏢 Using client:', {
        clientId: handleBarClient._id,
        name: handleBarClient.name,
      });

      const newUser = {
        clerkUserId: identity.subject,
        email: identity.email!,
        firstName: identity.givenName,
        lastName: identity.familyName,
        points: 0,
        tier: 'Bronze',
        clientId: handleBarClient._id,
        role: 'user',
        searchText:
          `${identity.givenName ?? ''} ${identity.familyName ?? ''} ${identity.email!}`.toLowerCase(),
      };

      console.log('📝 Creating user with data:', {
        clerkUserId: newUser.clerkUserId,
        email: newUser.email,
        firstName: newUser.firstName,
        clientId: newUser.clientId,
      });

      const newUserId = await ctx.db.insert('users', newUser);
      const createdUser = await ctx.db.get(newUserId);

      console.log('✅ User created successfully:', {
        newUserId,
        hasCreatedUser: !!createdUser,
      });

      return createdUser;
    } catch (error) {
      console.error('💥 Error in getOrCreateUser:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get a user's milestone progress.
 * This can only be called when we know the user exists.
 */
export const getUserMilestones = query({
  args: {},
  handler: async (ctx) => {
    console.log('🏆 getUserMilestones called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      console.log('🔐 Auth identity:', {
        hasIdentity: !!identity,
        subject: identity?.subject,
        email: identity?.email,
      });

      if (!identity) {
        console.error('❌ No identity found in getUserMilestones');
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      console.log('👤 User lookup result:', {
        hasUser: !!user,
        userId: user?._id,
        clerkUserId: identity.subject,
      });

      if (!user) {
        console.warn(
          '⚠️ User not found in getUserMilestones, returning empty array'
        );
        // This should ideally not happen in the main app flow
        // because getOrCreateUser ensures the user exists.
        return [];
      }

      const milestones = await ctx.db
        .query('userMilestoneProgress')
        .withIndex('by_user_id', (q) => q.eq('userId', user._id))
        .collect();

      console.log('🎯 Milestones found:', milestones.length);
      return milestones;
    } catch (error) {
      console.error('💥 Error in getUserMilestones:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get user's tier progress and information
 */
export const getUserTierProgress = query({
  args: {},
  handler: async (ctx) => {
    console.log('🏅 getUserTierProgress called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.error('❌ No identity found in getUserTierProgress');
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      if (!user) {
        console.warn('⚠️ User not found in getUserTierProgress');
        return null;
      }

      // Calculate current tier progress
      const tierProgress = calculateUserTier({
        currentPoints: user.points,
        currentTier: user.tier,
      });

      // Get user's tier advancement history
      const tierHistory = await getUserTierHistory(ctx, user._id);

      // Get user's percentile within their client
      const userPercentile = await getUserTierPercentile(
        ctx,
        user.tier,
        user.clientId || undefined
      );

      console.log('🎯 Tier progress calculated:', {
        currentTier: user.tier,
        points: user.points,
        progressToNext: tierProgress.progressPercentage,
      });

      return {
        currentTier: user.tier,
        points: user.points,
        tierProgress,
        tierHistory,
        userPercentile,
        tierThresholds: TIER_THRESHOLDS,
      };
    } catch (error) {
      console.error('💥 Error in getUserTierProgress:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get tier distribution statistics
 */
export const getTierStatistics = query({
  args: {},
  handler: async (ctx) => {
    console.log('📊 getTierStatistics called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.error('❌ No identity found in getTierStatistics');
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      if (!user) {
        console.warn('⚠️ User not found in getTierStatistics');
        return null;
      }

      // Get tier distribution for user's client
      const distribution = await getTierDistribution(
        ctx,
        user.clientId || undefined
      );

      console.log('📈 Tier distribution calculated:', {
        totalUsers: distribution.totalUsers,
        tierCounts: distribution.tierDistribution,
      });

      return {
        totalUsers: distribution.totalUsers,
        tierDistribution: distribution.tierDistribution,
        tierThresholds: TIER_THRESHOLDS,
      };
    } catch (error) {
      console.error('💥 Error in getTierStatistics:', error);
      throw error;
    }
  },
});

/**
 * Internal mutation to create or update a user from webhook
 */
export const internalStoreOrUpdateUser = internalMutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getUserByClerkId(ctx, args.clerkUserId);
    if (user) {
      await ctx.db.patch(user._id, {
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        searchText:
          `${args.firstName ?? ''} ${args.lastName ?? ''} ${args.email}`.toLowerCase(),
      });
    }
    // Note: We no longer create the user here, getOrCreateUser handles that.
  },
});

/**
 * Internal mutation to set a user's role from a webhook.
 */
export const internalSetUserRole = internalMutation({
  args: { clerkUserId: v.string(), role: v.string() },
  handler: async (ctx, { clerkUserId, role }) => {
    const user = await getUserByClerkId(ctx, clerkUserId);

    if (user) {
      await ctx.db.patch(user._id, { role });
      console.log(`✅ Updated role for user ${clerkUserId} to "${role}"`);
    } else {
      console.warn(
        `⚠️ Webhook tried to set role for non-existent user: ${clerkUserId}`
      );
    }
  },
});
