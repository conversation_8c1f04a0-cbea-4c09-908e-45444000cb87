import { internalMutation } from './_generated/server';

// This configuration is used to seed your database with an initial client.
const theHandleBarSeedConfig = {
  clientName: 'The Handle Bar Fitness Studio',
  slug: 'the-handle-bar',
  branding: {
    primaryColor: '#4F46E5',
    logoUrl: '/assets/clients/the-handle-bar/logo.png',
  },
  features: {
    tiersEnabled: true,
    leaderboardsEnabled: false,
    socialSharingActive: true,
  },
  milestones: [
    {
      id: 'first-class-theHandleBar',
      name: 'First Steps at Studio A',
      description: 'Complete your first class with us!',
      triggerType: 'activity_count',
      conditions: {
        activityTypeMatcher: 'class_attendance',
        countThreshold: 1,
      },
      rewards: [
        { type: 'points', value: 100 },
        { type: 'badge', value: 'badge_first_class_theHandleBar' },
      ],
      isRepeatable: false,
      isEnabled: true,
    },
  ],
  rewards: [
    {
      id: 'smoothie-theHandleBar',
      name: 'Free Smoothie',
      description: 'Enjoy a refreshing post-workout smoothie on us.',
      cost: 500,
    },
    {
      id: 'waterbottle-theHandleBar',
      name: 'Branded Water Bottle',
      description: 'Stay hydrated with our stylish water bottle.',
      cost: 1500,
    },
    {
      id: 'tshirt-theHandleBar',
      name: 'Studio T-Shirt',
      description: 'Represent The Handle Bar with our official tee.',
      cost: 2500,
    },
    {
      id: 'guestpass-theHandleBar',
      name: 'Free Guest Pass',
      description: 'Bring a friend to a class for free.',
      cost: 3000,
    },
    {
      id: 'merch_10_off-theHandleBar',
      name: '10% Off Merchandise',
      description: 'Get a discount on your next apparel purchase.',
      cost: 1000,
    },
    {
      id: 'pt_20_off-theHandleBar',
      name: '20% Off Personal Training',
      description: 'Level up your fitness with a discounted PT session.',
      cost: 5000,
    },
    {
      id: 'yogamat-theHandleBar',
      name: 'Premium Yoga Mat',
      description: 'A high-quality mat for your practice.',
      cost: 6000,
    },
    {
      id: 'classpack_5-theHandleBar',
      name: '5-Class Pack Discount',
      description: 'Get 15% off your next 5-class pack purchase.',
      cost: 7500,
    },
    {
      id: 'nutrition-consult-theHandleBar',
      name: 'Nutrition Consultation',
      description: 'A one-on-one session with our nutrition expert.',
      cost: 10000,
    },
    {
      id: 'month_free-theHandleBar',
      name: 'One Month Free Membership',
      description:
        'The ultimate prize! Enjoy a full month of unlimited classes.',
      cost: 25000,
    },
  ],
  dashboardLayout: {
    layout: 'grid',
    widgets: [
      { type: 'points_summary', position: 1, title: 'My Studio A Points' },
      { type: 'recent_activities', position: 2, config: { limit: 3 } },
      {
        type: 'milestone_progress_overview',
        position: 3,
        title: 'My Badge Collection',
      },
    ],
  },
  activityTypes: [
    { name: 'Class Attendance', key: 'class_attendance' },
    { name: 'Personal Training', key: 'personal_training' },
    { name: 'Workshop', key: 'workshop' },
  ],
};

export default internalMutation({
  handler: async (ctx) => {
    console.log('🌱 Seeding database...');

    const clientSlug = theHandleBarSeedConfig.slug;

    const existingClient = await ctx.db
      .query('clients')
      .withIndex('by_slug', (q) => q.eq('slug', clientSlug))
      .unique();

    let clientIdToUse;

    if (existingClient) {
      console.log(
        `Client "${clientSlug}" (ID: ${existingClient._id}) already exists. Updating config.`
      );
      clientIdToUse = existingClient._id;
      await ctx.db.patch(existingClient._id, {
        name: theHandleBarSeedConfig.clientName,
      });
    } else {
      clientIdToUse = await ctx.db.insert('clients', {
        name: theHandleBarSeedConfig.clientName,
        slug: clientSlug,
        createdAt: Date.now(),
      });
      console.log(
        `🌿 Seeded new client "${clientSlug}" with ID: ${clientIdToUse}`
      );
    }

    const existingClientConfig = await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', clientIdToUse))
      .unique();

    const configPayload: any = {
      clientId: clientIdToUse,
      branding: theHandleBarSeedConfig.branding,
      features: theHandleBarSeedConfig.features,
      milestones: theHandleBarSeedConfig.milestones,
      rewards: theHandleBarSeedConfig.rewards,
      dashboardLayout: theHandleBarSeedConfig.dashboardLayout,
    };

    // Rewards are now managed in their own table, so we remove them from the config payload.
    delete configPayload.rewards;

    if (existingClientConfig) {
      await ctx.db.patch(existingClientConfig._id, configPayload);
      console.log(`⚙️  Updated configuration for client ID: ${clientIdToUse}`);
    } else {
      await ctx.db.insert('clientConfiguration', configPayload);
      console.log(
        `⚙️  Seeded new configuration for client ID: ${clientIdToUse}`
      );
    }

    // Seed Activity Types
    for (const activityType of theHandleBarSeedConfig.activityTypes) {
      const existing = await ctx.db
        .query('activityTypes')
        .withIndex('by_client_id', (q) => q.eq('clientId', clientIdToUse))
        .filter((q) => q.eq(q.field('key'), activityType.key))
        .first();

      if (!existing) {
        await ctx.db.insert('activityTypes', {
          clientId: clientIdToUse,
          ...activityType,
        });
        console.log(`🏃‍♀️ Seeded activity type: ${activityType.name}`);
      }
    }

    // --- One-Time Milestone Migration ---
    // This logic moves milestones from the old clientConfiguration to the new table.
    const milestonesInNewTable = await ctx.db
      .query('milestones')
      .withIndex('by_client_id', (q) => q.eq('clientId', clientIdToUse))
      .collect();

    if (
      milestonesInNewTable.length === 0 &&
      theHandleBarSeedConfig.milestones.length > 0
    ) {
      console.log('🏁 Starting one-time milestone migration...');
      const adminUser = await ctx.db
        .query('users')
        .filter((q) => q.eq(q.field('email'), '<EMAIL>'))
        .first();
      if (adminUser) {
        for (const milestone of theHandleBarSeedConfig.milestones) {
          await ctx.db.insert('milestones', {
            clientId: clientIdToUse,
            name: milestone.name,
            description: milestone.description,
            triggerType: milestone.triggerType,
            conditions: milestone.conditions,
            rewards: milestone.rewards,
            isEnabled: milestone.isEnabled,
            isRepeatable: milestone.isRepeatable,
            createdBy: adminUser._id, // Assign to a default admin user
            lastModifiedBy: adminUser._id,
          });
          console.log(` migrated milestone: ${milestone.name}`);
        }
        console.log('✅ Milestone migration complete.');
      } else {
        console.warn(
          '⚠️ Could not run milestone migration. Default admin user not found.'
        );
      }
    }

    console.log('✅ Database seed process complete!');
  },
});
