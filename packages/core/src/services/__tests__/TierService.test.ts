/**
 * Unit tests for TierService
 */

import {
  calculateUserTier,
  getTierInfo,
  getNextTierInfo,
  calculateTierStatistics,
  TIER_THRESHOLDS,
  type TierCalculationInput,
} from '../TierService';

describe('TierService', () => {
  describe('calculateUserTier', () => {
    it('should calculate Bronze tier for 0 points', () => {
      const input: TierCalculationInput = {
        currentPoints: 0,
        currentTier: 'Bronze',
      };

      const result = calculateUserTier(input);

      expect(result.newTier).toBe('Bronze');
      expect(result.hasAdvanced).toBe(false);
      expect(result.pointsToNextTier).toBe(100); // Silver threshold
      expect(result.progressPercentage).toBe(0);
    });

    it('should calculate Silver tier for 150 points', () => {
      const input: TierCalculationInput = {
        currentPoints: 150,
        currentTier: 'Bronze',
      };

      const result = calculateUserTier(input);

      expect(result.newTier).toBe('Silver');
      expect(result.hasAdvanced).toBe(true);
      expect(result.pointsToNextTier).toBe(150); // Gold threshold - current points
      expect(result.progressPercentage).toBe(50); // 150/300 * 100
    });

    it('should calculate Gold tier for 350 points', () => {
      const input: TierCalculationInput = {
        currentPoints: 350,
        currentTier: 'Silver',
      };

      const result = calculateUserTier(input);

      expect(result.newTier).toBe('Gold');
      expect(result.hasAdvanced).toBe(true);
      expect(result.pointsToNextTier).toBe(150); // Platinum threshold - current points
    });

    it('should calculate Diamond tier for maximum points', () => {
      const input: TierCalculationInput = {
        currentPoints: 1000,
        currentTier: 'Platinum',
      };

      const result = calculateUserTier(input);

      expect(result.newTier).toBe('Diamond');
      expect(result.hasAdvanced).toBe(true);
      expect(result.pointsToNextTier).toBe(0); // No next tier
      expect(result.progressPercentage).toBe(100);
    });

    it('should not advance if already at correct tier', () => {
      const input: TierCalculationInput = {
        currentPoints: 150,
        currentTier: 'Silver',
      };

      const result = calculateUserTier(input);

      expect(result.newTier).toBe('Silver');
      expect(result.hasAdvanced).toBe(false);
    });
  });

  describe('getTierInfo', () => {
    it('should return correct tier info for Bronze', () => {
      const tierInfo = getTierInfo('Bronze');

      expect(tierInfo).toEqual({
        tier: 'Bronze',
        pointsRequired: 0,
        color: '#CD7F32',
        icon: '🥉',
      });
    });

    it('should return correct tier info for Diamond', () => {
      const tierInfo = getTierInfo('Diamond');

      expect(tierInfo).toEqual({
        tier: 'Diamond',
        pointsRequired: 1000,
        color: '#B9F2FF',
        icon: '💎',
      });
    });

    it('should return null for invalid tier', () => {
      const tierInfo = getTierInfo('InvalidTier');
      expect(tierInfo).toBeNull();
    });
  });

  describe('getNextTierInfo', () => {
    it('should return Silver for Bronze tier', () => {
      const nextTier = getNextTierInfo('Bronze');

      expect(nextTier).toEqual({
        tier: 'Silver',
        pointsRequired: 100,
        color: '#C0C0C0',
        icon: '🥈',
      });
    });

    it('should return null for Diamond tier', () => {
      const nextTier = getNextTierInfo('Diamond');
      expect(nextTier).toBeNull();
    });

    it('should return null for invalid tier', () => {
      const nextTier = getNextTierInfo('InvalidTier');
      expect(nextTier).toBeNull();
    });
  });

  describe('calculateTierStatistics', () => {
    it('should calculate correct statistics for mixed tiers', () => {
      const userTiers = ['Bronze', 'Silver', 'Silver', 'Gold', 'Bronze'];

      const stats = calculateTierStatistics(userTiers, 'Silver');

      expect(stats.userPercentile).toBe(60); // 3 users at or below Silver out of 5
      expect(stats.tierDistribution).toEqual({
        Bronze: 2,
        Silver: 2,
        Gold: 1,
        Platinum: 0,
        Diamond: 0,
      });
    });

    it('should handle empty user list', () => {
      const stats = calculateTierStatistics([], 'Bronze');

      expect(stats.userPercentile).toBe(0);
      expect(stats.tierDistribution).toEqual({
        Bronze: 0,
        Silver: 0,
        Gold: 0,
        Platinum: 0,
        Diamond: 0,
      });
    });

    it('should calculate 100th percentile for Diamond user', () => {
      const userTiers = ['Bronze', 'Silver', 'Gold', 'Diamond'];

      const stats = calculateTierStatistics(userTiers, 'Diamond');

      expect(stats.userPercentile).toBe(100);
    });
  });

  describe('TIER_THRESHOLDS', () => {
    it('should have correct number of tiers', () => {
      expect(TIER_THRESHOLDS).toHaveLength(5);
    });

    it('should be sorted by points required', () => {
      for (let i = 1; i < TIER_THRESHOLDS.length; i++) {
        expect(TIER_THRESHOLDS[i].pointsRequired).toBeGreaterThan(
          TIER_THRESHOLDS[i - 1].pointsRequired
        );
      }
    });

    it('should have unique tier names', () => {
      const tierNames = TIER_THRESHOLDS.map(t => t.tier);
      const uniqueNames = new Set(tierNames);
      expect(uniqueNames.size).toBe(tierNames.length);
    });
  });
});
