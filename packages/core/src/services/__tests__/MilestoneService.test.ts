/**
 * Unit tests for MilestoneService
 */

import {
  evaluateUserMilestones,
  type MilestoneEvaluationInput,
  type Milestone,
  type UserActivity,
} from '../MilestoneService';

describe('MilestoneService', () => {
  const mockMilestones: Milestone[] = [
    {
      id: 'milestone-1',
      name: 'First Class',
      description: 'Attend your first class',
      triggerType: 'activity_count',
      conditions: {
        activityTypeMatcher: 'class_attendance',
        countThreshold: 1,
      },
      rewards: [
        { type: 'points', value: 50 },
        { type: 'badge', value: 'first-class' },
      ],
      isEnabled: true,
      isRepeatable: false,
    },
    {
      id: 'milestone-2',
      name: 'Class Regular',
      description: 'Attend 10 classes',
      triggerType: 'activity_count',
      conditions: {
        activityTypeMatcher: 'class_attendance',
        countThreshold: 10,
      },
      rewards: [
        { type: 'points', value: 200 },
        { type: 'badge', value: 'regular' },
      ],
      isEnabled: true,
      isRepeatable: false,
    },
    {
      id: 'milestone-3',
      name: 'Weekly Warrior',
      description: 'Attend a class (repeatable)',
      triggerType: 'activity_count',
      conditions: {
        activityTypeMatcher: 'class_attendance',
        countThreshold: 1,
      },
      rewards: [{ type: 'points', value: 10 }],
      isEnabled: true,
      isRepeatable: true,
    },
  ];

  const mockActivities: UserActivity[] = [
    {
      id: 'activity-1',
      activityType: 'class_attendance',
      timestamp: Date.now() - 86400000, // 1 day ago
    },
    {
      id: 'activity-2',
      activityType: 'class_attendance',
      timestamp: Date.now() - 172800000, // 2 days ago
    },
  ];

  describe('evaluateUserMilestones', () => {
    it('should achieve first milestone with one activity', () => {
      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [mockActivities[0]],
        availableMilestones: [mockMilestones[0]],
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(1);
      expect(result.newlyAchieved[0].milestoneId).toBe('milestone-1');
      expect(result.pointsAwarded).toBe(50);
      expect(result.badgesEarned).toEqual(['first-class']);
    });

    it('should not achieve milestone if already achieved and not repeatable', () => {
      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [mockActivities[0]],
        availableMilestones: [mockMilestones[0]],
        achievedMilestones: ['milestone-1'],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(0);
      expect(result.pointsAwarded).toBe(0);
      expect(result.badgesEarned).toHaveLength(0);
    });

    it('should achieve repeatable milestone even if already achieved', () => {
      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [mockActivities[0]],
        availableMilestones: [mockMilestones[2]], // Weekly Warrior (repeatable)
        achievedMilestones: ['milestone-3'],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(1);
      expect(result.newlyAchieved[0].milestoneId).toBe('milestone-3');
      expect(result.pointsAwarded).toBe(10);
    });

    it('should not achieve milestone if threshold not met', () => {
      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [mockActivities[0]], // Only 1 activity
        availableMilestones: [mockMilestones[1]], // Requires 10 activities
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(0);
      expect(result.pointsAwarded).toBe(0);
    });

    it('should achieve milestone when threshold is exactly met', () => {
      const tenActivities = Array.from({ length: 10 }, (_, i) => ({
        id: `activity-${i}`,
        activityType: 'class_attendance',
        timestamp: Date.now() - i * 86400000,
      }));

      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: tenActivities,
        availableMilestones: [mockMilestones[1]], // Requires 10 activities
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(1);
      expect(result.newlyAchieved[0].milestoneId).toBe('milestone-2');
      expect(result.pointsAwarded).toBe(200);
      expect(result.badgesEarned).toEqual(['regular']);
    });

    it('should achieve multiple milestones in one evaluation', () => {
      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [mockActivities[0]],
        availableMilestones: [mockMilestones[0], mockMilestones[2]], // First Class + Weekly Warrior
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(2);
      expect(result.pointsAwarded).toBe(60); // 50 + 10
      expect(result.badgesEarned).toEqual(['first-class']);
    });

    it('should not evaluate disabled milestones', () => {
      const disabledMilestone = {
        ...mockMilestones[0],
        isEnabled: false,
      };

      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [mockActivities[0]],
        availableMilestones: [disabledMilestone],
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(0);
      expect(result.pointsAwarded).toBe(0);
    });

    it('should only count matching activity types', () => {
      const mixedActivities = [
        ...mockActivities,
        {
          id: 'activity-3',
          activityType: 'gym_visit',
          timestamp: Date.now(),
        },
      ];

      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: mixedActivities,
        availableMilestones: [mockMilestones[0]], // Requires 1 class_attendance
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(1);
      // Should still achieve because we have 2 class_attendance activities
    });

    it('should handle empty inputs gracefully', () => {
      const input: MilestoneEvaluationInput = {
        activityType: 'class_attendance',
        userActivities: [],
        availableMilestones: [],
        achievedMilestones: [],
      };

      const result = evaluateUserMilestones(input);

      expect(result.newlyAchieved).toHaveLength(0);
      expect(result.pointsAwarded).toBe(0);
      expect(result.badgesEarned).toHaveLength(0);
    });
  });
});
