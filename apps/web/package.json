{"name": "@fitness-rewards/web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@clerk/clerk-react": "^5.0.0", "@fitness-rewards/db": "workspace:*", "convex": "^1.24.8", "dayjs": "^1.11.11", "lucide-react": "^0.378.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.23.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/react": "^18.3.2", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.2.11", "vite-tsconfig-paths": "^4.3.2"}}