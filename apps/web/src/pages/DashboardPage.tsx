import { useMutation, useQuery } from 'convex/react';
import { api } from '@fitness-rewards/db';
import LogClassAttendanceButton from '../components/LogClassAttendanceButton';
import TierWidget from '../components/TierWidget';
import { useEffect } from 'react';
import { Doc } from '@fitness-rewards/db/convex/_generated/dataModel';
import { WithLoading } from '../components/utils/WithLoading';

/**
 * Entry point for the Dashboard. Handles initial user loading and creation.
 */
export default function DashboardPage() {
  const user = useQuery(api.functions.users.getCurrentUser);
  const getOrCreateUser = useMutation(api.functions.users.getOrCreateUser);

  // Only fetch other data if we have a user
  const skipQueries = user === undefined || user === null;
  const activities = useQuery(
    api.functions.activities.getRecentActivities,
    skipQueries ? 'skip' : {}
  );
  const milestones = useQuery(
    api.functions.users.getUserMilestones,
    skipQueries ? 'skip' : {}
  );
  const tierProgress = useQuery(
    api.functions.users.getUserTierProgress,
    skipQueries ? 'skip' : {}
  );
  const tierStats = useQuery(
    api.functions.users.getTierStatistics,
    skipQueries ? 'skip' : {}
  );

  useEffect(() => {
    // If the user is logged in but doesn't have a DB record yet, create one.
    if (user === null) {
      getOrCreateUser().catch(console.error);
    }
  }, [user, getOrCreateUser]);

  // Show loading while user is being fetched or created
  if (user === undefined || user === null) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 text-center">
          <WithLoading isPending={true} spinnerSize="sm">
            <h1 className="text-3xl font-bold">Welcome, Fitness Enthusiast!</h1>
          </WithLoading>
          <p className="text-gray-600">Track your progress and earn rewards.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold">
          Welcome, {user.firstName || 'Fitness Enthusiast'}!
        </h1>
        <p className="text-gray-600">Track your progress and earn rewards.</p>
      </div>

      <div className="mb-8 flex justify-center">
        <LogClassAttendanceButton />
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <WithLoading
            isPending={tierProgress === undefined || tierStats === undefined}
          >
            {tierProgress && tierStats && (
              <TierWidget tierProgress={tierProgress} tierStats={tierStats} />
            )}
          </WithLoading>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Quick Stats</h2>
          <div className="text-4xl font-bold text-indigo-600">
            {user.points.toLocaleString()}
          </div>
          <p className="mt-2 text-sm text-gray-500">Total Points Earned</p>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Recent Activities</h2>
          <WithLoading isPending={activities === undefined}>
            {activities && activities.length === 0 ? (
              <p className="text-gray-500">No activities yet.</p>
            ) : (
              <ul className="space-y-2">
                {activities?.map((act) => (
                  <li key={act._id} className="text-sm">
                    {formatActivityType(act.activityType)}
                  </li>
                ))}
              </ul>
            )}
          </WithLoading>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Achievements</h2>
          <WithLoading isPending={milestones === undefined}>
            {milestones && milestones.length === 0 ? (
              <p className="text-gray-500">No achievements yet.</p>
            ) : (
              <ul className="space-y-2">
                {milestones?.map((milestone) => (
                  <li key={milestone._id} className="text-sm">
                    🏆 {milestone.name}
                  </li>
                ))}
              </ul>
            )}
          </WithLoading>
        </div>
      </div>
    </div>
  );
}

// Helper Functions

/**
 * Formats an activity type string for display by converting snake_case to Title Case.
 * @param type - The activity type string (e.g., "class_attendance")
 * @returns The formatted activity type string (e.g., "Class Attendance")
 */
function formatActivityType(type: string | undefined): string {
  if (!type || typeof type !== 'string') {
    return 'Unknown Activity';
  }

  return type
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
