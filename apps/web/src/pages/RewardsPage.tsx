import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@db';
import toast from 'react-hot-toast';

import RewardCard from '../components/RewardCard';
import RedemptionConfirmationModal from '../components/RedemptionConfirmationModal';
import { WithLoading } from '../components/utils/WithLoading';
import { Doc } from '@db/types';

const RewardsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('catalog');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Doc<'rewards'> | null>(
    null
  );

  const rewards = useQuery(api.functions.rewards.getRewardsCatalog);
  const history = useQuery(api.functions.rewards.getRedemptionHistory);
  const user = useQuery(api.functions.users.getCurrentUser);
  const redeemReward = useMutation(api.functions.rewards.redeemReward);

  const handleRedeemClick = (reward: Doc<'rewards'>) => {
    setSelectedReward(reward);
    setIsModalOpen(true);
  };

  const handleConfirmRedemption = async () => {
    if (!selectedReward) return;

    try {
      await redeemReward({ rewardId: selectedReward._id });
      toast.success(`Successfully redeemed ${selectedReward.name}!`);
      setIsModalOpen(false);
      setSelectedReward(null);
      setActiveTab('history');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'An unknown error occurred';
      toast.error(`Redemption failed: ${errorMessage}`);
      console.error('Redemption error:', error);
    }
  };

  const handleCancelRedemption = () => {
    setIsModalOpen(false);
    setSelectedReward(null);
  };

  const renderContent = () => {
    if (activeTab === 'catalog') {
      return (
        <WithLoading isPending={rewards === undefined}>
          {rewards && rewards.length === 0 && (
            <div className="py-12 text-center">
              <h3 className="text-lg font-medium text-gray-900">
                No Rewards Available
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Check back later for new rewards!
              </p>
            </div>
          )}
          {rewards && rewards.length > 0 && (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {rewards
                .sort((a, b) => a.cost - b.cost)
                .map((reward) => (
                  <RewardCard
                    key={reward._id}
                    reward={reward}
                    userPoints={user?.points ?? 0}
                    onRedeem={() => handleRedeemClick(reward)}
                  />
                ))}
            </div>
          )}
        </WithLoading>
      );
    }

    // History Tab
    return (
      <WithLoading isPending={history === undefined}>
        {history && history.length === 0 && (
          <div className="py-12 text-center">
            <h3 className="text-lg font-medium text-gray-900">
              No Redemptions Yet
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Redeem a reward from the catalog to see it here.
            </p>
          </div>
        )}
        {history && history.length > 0 && (
          <div className="flow-root">
            <ul role="list" className="-mb-8">
              {history.map((item, itemIdx) => (
                <li key={item._id}>
                  <div className="relative pb-8">
                    {itemIdx !== history.length - 1 ? (
                      <span
                        className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex items-center space-x-3">
                      <div>
                        <span className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-500 ring-8 ring-white">
                          <svg
                            className="h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 justify-between sm:flex">
                        <div>
                          <p className="text-sm text-gray-500">
                            Redeemed{' '}
                            <span className="font-medium text-gray-900">
                              {item.rewardName}
                            </span>
                          </p>
                        </div>
                        <div className="mt-2 sm:mt-0">
                          <p className="text-sm text-gray-500">
                            <span className="font-medium text-red-500">
                              -{item.pointsSpent.toLocaleString()} pts
                            </span>
                            {' on '}
                            {new Date(item._creationTime).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </WithLoading>
    );
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Rewards</h1>
            <p className="text-gray-500">
              Spend your points on exclusive rewards.
            </p>
          </div>
          <WithLoading isPending={user === undefined} spinnerSize="sm">
            <div className="text-right">
              <p className="text-sm text-gray-500">Your Points</p>
              <p className="text-2xl font-bold text-indigo-600">
                {user?.points?.toLocaleString() ?? '...'}
              </p>
            </div>
          </WithLoading>
        </div>

        <div>
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('catalog')}
                className={`${
                  activeTab === 'catalog'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                } whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium`}
              >
                Catalog
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`${
                  activeTab === 'history'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                } whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium`}
              >
                History
              </button>
            </nav>
          </div>
          <div className="mt-6">{renderContent()}</div>
        </div>
      </div>
      <RedemptionConfirmationModal
        isOpen={isModalOpen}
        reward={selectedReward}
        userPoints={user?.points}
        isRedeeming={redeemReward.isLoading}
        onConfirm={handleConfirmRedemption}
        onCancel={handleCancelRedemption}
      />
    </>
  );
};

export default RewardsPage;
