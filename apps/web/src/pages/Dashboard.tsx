import { useQuery } from 'convex/react';
import { api } from '@fitness-rewards/db';
import LogClassAttendanceButton from '../components/LogClassAttendanceButton';
import { Doc } from '@fitness-rewards/db/convex/_generated/dataModel';

/**
 * The main dashboard content, rendered only when the user is fully loaded and synced.
 * It receives the user object as a prop and can safely assume it exists.
 */
export default function Dashboard({ user }: { user: Doc<'users'> }) {
  const activities = useQuery(api.functions.activities.getRecentActivities);
  const milestones = useQuery(api.functions.users.getUserMilestones);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">
          Welcome, {user.firstName || 'Fitness Enthusiast'}!
        </h1>
        <p className="text-gray-600">Track your progress and earn rewards.</p>
      </div>

      <div className="mb-8 flex justify-center">
        <LogClassAttendanceButton />
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* Points Widget */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">My Points</h2>
          <div className="text-4xl font-bold text-indigo-600">
            {user.points}
          </div>
          <p className="mt-2 text-sm text-gray-500">
            Tier: <span className="font-medium">{user.tier}</span>
          </p>
        </div>

        {/* Activities Widget */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Recent Activities</h2>
          {activities === undefined && 'Loading...'}
          {activities && activities.length === 0 && 'No activities yet.'}
          {activities && activities.length > 0 && (
            <ul className="space-y-2">
              {activities.map((act) => (
                <li key={act._id}>{formatActivityType(act.activityType)}</li>
              ))}
            </ul>
          )}
        </div>

        {/* Achievements Widget */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Achievements</h2>
          {milestones === undefined && 'Loading...'}
          {milestones && milestones.length === 0 && 'No achievements yet.'}
          {milestones && milestones.length > 0 && (
            <ul className="space-y-2">
              {milestones.map((ms) => (
                <li key={ms._id}>🏆 {ms.milestoneId}</li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper Functions
function formatActivityType(type: string): string {
  return type
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
