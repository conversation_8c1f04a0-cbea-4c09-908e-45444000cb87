import { useState } from 'react';
import RewardsManagementTab from '../components/admin/RewardsManagementTab';
import RedemptionFulfillmentTab from '../components/admin/RedemptionFulfillmentTab';
import MilestonesManagementTab from '../components/admin/MilestonesManagementTab';
import { Id } from '@db/types';

const TABS = {
  rewards: {
    label: 'Rewards Management',
  },
  milestones: {
    label: 'Milestone Management',
  },
  redemptions: {
    label: 'Redemption Fulfillment',
  },
};

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<keyof typeof TABS>('rewards');

  return (
    <div className="container mx-auto px-4 py-8">
      <div>
        <h1 className="text-3xl font-bold">Client Admin Dashboard</h1>
        <p className="text-gray-600">
          Manage your rewards and member redemptions.
        </p>
      </div>
      <div className="mt-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {Object.entries(TABS).map(([key, tab]) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as keyof typeof TABS)}
                className={`${
                  activeTab === key
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                } whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
        <div className="mt-6">
          {activeTab === 'rewards' && <RewardsManagementTab />}
          {activeTab === 'milestones' && <MilestonesManagementTab />}
          {activeTab === 'redemptions' && <RedemptionFulfillmentTab />}
        </div>
      </div>
    </div>
  );
}
