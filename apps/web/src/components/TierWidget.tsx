import React from 'react';
import { InferQueryOutput } from 'convex/react';
import { api } from '@fitness-rewards/db';

// Define prop types from query outputs
type TierProgressData = InferQueryOutput<
  typeof api.functions.users.getUserTierProgress
>;
type TierStatsData = InferQueryOutput<
  typeof api.functions.users.getTierStatistics
>;

/**
 * TierWidget - Displays user's tier status and progress.
 * This is now a presentational component that receives all data via props.
 */
export default function TierWidget({
  tierProgress,
  tierStats,
}: {
  tierProgress: NonNullable<TierProgressData>;
  tierStats: NonNullable<TierStatsData>;
}) {
  const {
    currentTier,
    points,
    tierProgress: progress,
    userPercentile,
  } = tierProgress;

  const currentTierInfo = progress.tierThresholds.find(
    (t) => t.tier === currentTier
  );
  const nextTierInfo = progress.tierThresholds.find(
    (t) => t.pointsRequired > points
  );

  return (
    <div className="rounded-lg bg-white p-6 shadow-md">
      {/* Header */}
      <h2 className="mb-4 text-xl font-semibold">Your Tier Status</h2>

      {/* Current Tier Display */}
      <div className="mb-6 flex items-center space-x-4">
        <div
          className="flex h-16 w-16 items-center justify-center rounded-full text-2xl"
          style={{
            backgroundColor: currentTierInfo?.color + '20',
            border: `3px solid ${currentTierInfo?.color}`,
          }}
        >
          {currentTierInfo?.icon}
        </div>
        <div>
          <h3
            className="text-2xl font-bold"
            style={{ color: currentTierInfo?.color }}
          >
            {currentTier}
          </h3>
          <p className="text-gray-600">{points.toLocaleString()} points</p>
        </div>
      </div>

      {/* Progress to Next Tier */}
      {nextTierInfo ? (
        <div className="mb-4">
          <div className="mb-2 flex justify-between text-sm">
            <span className="text-gray-600">
              Progress to {nextTierInfo.tier}
            </span>
            <span className="font-medium">
              {progress.pointsToNextTier.toLocaleString()} points needed
            </span>
          </div>

          {/* Progress Bar */}
          <div className="h-3 w-full rounded-full bg-gray-200">
            <div
              className="h-3 rounded-full transition-all duration-500 ease-out"
              style={{
                width: `${progress.progressPercentage}%`,
                background: `linear-gradient(90deg, ${currentTierInfo?.color}, ${nextTierInfo.color})`,
              }}
            />
          </div>

          <div className="mt-2 text-center text-sm text-gray-600">
            {Math.round(progress.progressPercentage)}% complete
          </div>
        </div>
      ) : (
        <div className="mb-4 text-center">
          <div className="mb-2 rounded-lg bg-gradient-to-r from-yellow-400 to-yellow-600 p-3 text-white">
            <span className="font-bold">🏆 Maximum Tier Achieved! 🏆</span>
          </div>
          <p className="text-sm text-gray-600">
            You've reached the highest tier possible. Amazing work!
          </p>
        </div>
      )}

      {/* Community Context */}
      <div className="mt-4 rounded-lg bg-gray-50 p-3">
        <p className="text-center text-sm text-gray-700">
          You're in the{' '}
          <span className="font-bold">top {100 - userPercentile}%</span> of
          members
        </p>
        <div className="mt-2 text-center text-xs text-gray-500">
          {tierStats.totalUsers} total members
        </div>
      </div>

      {/* Tier Advancement History Preview */}
      {tierProgress.tierHistory && tierProgress.tierHistory.length > 0 && (
        <div className="mt-4">
          <h4 className="mb-2 font-medium text-gray-800">Recent Advancement</h4>
          <div className="text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <span className="font-medium">
                {tierProgress.tierHistory[0].previousTier} →{' '}
                {tierProgress.tierHistory[0].newTier}
              </span>
              <span className="text-xs">
                {new Date(
                  tierProgress.tierHistory[0].advancedAt
                ).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Motivational Message */}
      <div className="mt-4 rounded-lg bg-blue-50 p-3">
        <p className="text-sm text-blue-800">
          {nextTierInfo
            ? `Keep it up! ${progress.pointsToNextTier} more points until ${nextTierInfo.tier}.`
            : "You've achieved the highest tier! Keep maintaining your amazing consistency."}
        </p>
      </div>
    </div>
  );
}

/**
 * Compact TierBadge component for use in other areas
 */
export function TierBadge({
  tier,
  tierThresholds,
  size = 'sm',
}: {
  tier: string;
  tierThresholds: NonNullable<TierProgressData>['tierThresholds'];
  size?: 'sm' | 'md' | 'lg';
}) {
  const tierInfo = tierThresholds.find((t) => t.tier === tier);

  if (!tierInfo) return null;

  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-12 w-12 text-lg',
  };

  return (
    <div
      className={`flex items-center justify-center rounded-full font-bold text-white ${sizeClasses[size]}`}
      style={{ backgroundColor: tierInfo.color }}
      title={`${tier} tier`}
    >
      {tierInfo.icon}
    </div>
  );
}
