import React from 'react';
import { useAuth } from '@clerk/clerk-react';
import { Navigate, useLocation } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { api } from '@db';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

/**
 * ProtectedRoute component that handles authentication and authorization.
 * Shows loading state when user data is being fetched or created.
 */
export default function ProtectedRoute({
  children,
  allowedRoles,
}: ProtectedRouteProps) {
  const { userId, isLoaded } = useAuth();
  const user = useQuery(api.functions.users.getCurrentUser);
  const location = useLocation();

  const userRole = user?.role ?? 'member';

  // Show loading while Clerk is initializing
  if (!isLoaded) {
    return null;
  }

  // Redirect to sign-in if not authenticated
  if (!userId) {
    return <Navigate to="/sign-in" state={{ from: location }} replace />;
  }

  // Show loading while user data is being fetched/created
  // This handles the case where user is authenticated with <PERSON> but not yet in Convex DB
  if (user === undefined) {
    return null;
  }

  // If user is null, it means they're authenticated but don't exist in our DB yet
  // Let the child component (e.g., DashboardPage) handle user creation
  if (user === null) {
    return <>{children}</>;
  }

  // Check role-based authorization
  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
