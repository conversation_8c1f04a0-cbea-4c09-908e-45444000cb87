import { useAttendanceButton } from '../hooks/useAttendanceButton';
import {
  LoadingSpinner,
  CheckIcon,
  CalendarCheckIcon,
} from './icons/AttendanceIcons';

/**
 * LogClassAttendanceButton - Component for logging class attendance.
 *
 * Implements all required states:
 * - Default: Enabled button with "Log Class Attendance" text
 * - Loading: Disabled button with spinner and "Logging..." text
 * - Success: Brief confirmation with checkmark, then return to default
 * - Cooldown: Temporarily disabled with "Logged!" text (prevents spam)
 * - Error: Red border/text with error message display
 *
 * @returns {JSX.Element} The attendance button component
 */
export default function LogClassAttendanceButton(): JSX.Element {
  const { buttonState, errorMessage, handleClick } = useAttendanceButton();

  /**
   * Gets the button configuration based on current state.
   * @returns {Object} Button configuration with text, styling, and icon
   */
  const getButtonConfig = () => {
    switch (buttonState) {
      case 'loading':
        return {
          text: 'Logging...',
          disabled: true,
          className: 'bg-gray-400 cursor-not-allowed',
          icon: <LoadingSpinner />,
        };
      case 'success':
        return {
          text: 'Logged Successfully!',
          disabled: true,
          className: 'bg-green-500 hover:bg-green-600',
          icon: <CheckIcon />,
        };
      case 'cooldown':
        return {
          text: 'Logged!',
          disabled: true,
          className: 'bg-gray-400 cursor-not-allowed',
          icon: null,
        };
      case 'error':
        return {
          text: 'Try Again',
          disabled: false,
          className:
            'bg-white border-2 border-red-500 text-red-500 hover:bg-red-50',
          icon: null,
        };
      default:
        return {
          text: 'Log Class Attendance',
          disabled: false,
          className: 'bg-indigo-600 hover:bg-indigo-700',
          icon: <CalendarCheckIcon />,
        };
    }
  };

  const config = getButtonConfig();

  return (
    <div className="flex flex-col items-center">
      <button
        onClick={handleClick}
        disabled={config.disabled}
        className={`flex items-center justify-center rounded-lg px-6 py-3 font-medium text-white transition-all duration-200 ${config.className}`}
        aria-label="Log class attendance"
      >
        {config.icon && <span className="mr-2">{config.icon}</span>}
        {config.text}
      </button>

      {buttonState === 'error' && (
        <div className="mt-2 text-sm text-red-500">
          {errorMessage || 'Failed to log attendance. Please try again.'}
        </div>
      )}
    </div>
  );
}
