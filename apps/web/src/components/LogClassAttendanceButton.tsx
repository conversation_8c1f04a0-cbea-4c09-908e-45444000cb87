import { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@fitness-rewards/db';
import { toast } from 'react-hot-toast';

/**
 * LogClassAttendanceButton - Component for logging class attendance
 *
 * Implements all required states:
 * - Default: Enabled button with "Log Class Attendance" text
 * - Loading: Disabled button with spinner and "Logging..." text
 * - Success: Brief confirmation with checkmark, then return to default
 * - Cooldown: Temporarily disabled with "Logged!" text (prevents spam)
 * - Error: Red border/text with error message display
 */
export default function LogClassAttendanceButton() {
  // Button states
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [cooldownActive, setCooldownActive] = useState(false);

  // Convex mutation
  const logAttendance = useMutation(
    api.functions.activities.logClassAttendance
  );

  // Reset error state after 5 seconds
  useEffect(() => {
    if (isError) {
      const timer = setTimeout(() => {
        setIsError(false);
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isError]);

  // Reset success state and handle cooldown
  useEffect(() => {
    if (isSuccess) {
      // Show success state briefly
      const successTimer = setTimeout(() => {
        setIsSuccess(false);
        setCooldownActive(true);

        // Enable button after cooldown period (1 minute)
        const cooldownTimer = setTimeout(() => {
          setCooldownActive(false);
        }, 60000); // 1 minute cooldown

        return () => clearTimeout(cooldownTimer);
      }, 2000);

      return () => clearTimeout(successTimer);
    }
  }, [isSuccess]);

  const handleClick = async () => {
    if (isLoading || cooldownActive) return;

    setIsLoading(true);
    setIsError(false);

    try {
      const result = await logAttendance();
      setIsLoading(false);
      setIsSuccess(true);

      // Show toast notification for points earned
      toast.success(`You earned ${result.totalPoints} points!`);

      // Show milestone achievement notifications
      if (result.newMilestones.length > 0) {
        result.newMilestones.forEach((milestone) => {
          toast.success(`🏆 Achievement Unlocked: ${milestone.name}`, {
            duration: 5000,
            icon: '🏆',
          });
        });
      }

      // Show tier advancement notification
      if (result.tierAdvancement) {
        toast.success(
          `🎉 Tier Advanced! You've reached ${result.tierAdvancement.newTier} tier!`,
          {
            duration: 6000,
            icon: '🎉',
          }
        );
      }
    } catch (error) {
      setIsLoading(false);
      setIsError(true);
      setErrorMessage(
        error instanceof Error ? error.message : 'An error occurred'
      );
      console.error('Failed to log attendance:', error);
    }
  };

  // Determine button state and styling
  const getButtonState = () => {
    if (isLoading) {
      return {
        text: 'Logging...',
        disabled: true,
        className: 'bg-gray-400 cursor-not-allowed',
        icon: <LoadingSpinner />,
      };
    }

    if (isSuccess) {
      return {
        text: 'Logged Successfully!',
        disabled: true,
        className: 'bg-green-500 hover:bg-green-600',
        icon: <CheckIcon />,
      };
    }

    if (cooldownActive) {
      return {
        text: 'Logged!',
        disabled: true,
        className: 'bg-gray-400 cursor-not-allowed',
        icon: null,
      };
    }

    if (isError) {
      return {
        text: 'Try Again',
        disabled: false,
        className:
          'bg-white border-2 border-red-500 text-red-500 hover:bg-red-50',
        icon: null,
      };
    }

    return {
      text: 'Log Class Attendance',
      disabled: false,
      className: 'bg-indigo-600 hover:bg-indigo-700',
      icon: <CalendarCheckIcon />,
    };
  };

  const buttonState = getButtonState();

  return (
    <div className="flex flex-col items-center">
      <button
        onClick={handleClick}
        disabled={buttonState.disabled}
        className={`flex items-center justify-center rounded-lg px-6 py-3 font-medium text-white transition-all duration-200 ${buttonState.className}`}
        aria-label="Log class attendance"
      >
        {buttonState.icon && <span className="mr-2">{buttonState.icon}</span>}
        {buttonState.text}
      </button>

      {isError && (
        <div className="mt-2 text-sm text-red-500">
          {errorMessage || 'Failed to log attendance. Please try again.'}
        </div>
      )}
    </div>
  );
}

/**
 * Loading spinner icon component.
 * @returns {JSX.Element} Animated loading spinner SVG
 */
const LoadingSpinner = (): JSX.Element => (
  <svg
    className="h-5 w-5 animate-spin text-white"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
);

/**
 * Check mark icon component for success state.
 * @returns {JSX.Element} Check mark SVG icon
 */
const CheckIcon = (): JSX.Element => (
  <svg
    className="h-5 w-5 text-white"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 13l4 4L19 7"
    />
  </svg>
);

/**
 * Calendar with check mark icon component for default state.
 * @returns {JSX.Element} Calendar check SVG icon
 */
const CalendarCheckIcon = (): JSX.Element => (
  <svg
    className="h-5 w-5 text-white"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 14l2 2 4-4"
    />
  </svg>
);
