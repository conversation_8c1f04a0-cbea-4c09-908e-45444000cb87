import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Doc } from '@db/types';

interface RewardFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  reward?: Doc<'rewards'>;
}

const RewardFormModal: React.FC<RewardFormModalProps> = ({
  isOpen,
  onClose,
  reward,
}) => {
  const [name, setName] = useState('');
  const [cost, setCost] = useState(100);
  const [description, setDescription] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [isActive, setIsActive] = useState(true);

  const createReward = useMutation(api.functions.admin.createReward);
  const updateReward = useMutation(api.functions.admin.updateReward);

  const isEditing = reward !== undefined;

  useEffect(() => {
    if (reward) {
      setName(reward.name);
      setCost(reward.cost);
      setDescription(reward.description || '');
      setImageUrl(reward.imageUrl || '');
      setIsActive(reward.isActive);
    } else {
      // Reset form for creation
      setName('');
      setCost(100);
      setDescription('');
      setImageUrl('');
      setIsActive(true);
    }
  }, [reward, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || cost <= 0) {
      toast.error('Name and a positive point cost are required.');
      return;
    }

    const mutationPromise = isEditing
      ? updateReward({
          rewardId: reward._id,
          name,
          cost,
          description,
          imageUrl,
          isActive,
        })
      : createReward({ name, cost, description, imageUrl, isActive });

    await toast.promise(mutationPromise, {
      loading: `${isEditing ? 'Updating' : 'Creating'} reward...`,
      success: `Reward successfully ${isEditing ? 'updated' : 'created'}!`,
      error: `Failed to ${isEditing ? 'update' : 'create'} reward.`,
    });

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="w-full max-w-lg rounded-lg bg-white p-6 shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-2xl font-bold text-gray-800">
          {isEditing ? 'Edit Reward' : 'Create New Reward'}
        </h2>
        <form onSubmit={handleSubmit} className="mt-4 space-y-4">
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700"
            >
              Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label
              htmlFor="cost"
              className="block text-sm font-medium text-gray-700"
            >
              Point Cost
            </label>
            <input
              type="number"
              id="cost"
              value={cost}
              onChange={(e) => setCost(Number(e.target.value))}
              required
              min="1"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700"
            >
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label
              htmlFor="imageUrl"
              className="block text-sm font-medium text-gray-700"
            >
              Image URL
            </label>
            <input
              type="text"
              id="imageUrl"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div className="flex items-center">
            <input
              id="isActive"
              type="checkbox"
              checked={isActive}
              onChange={(e) => setIsActive(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <label
              htmlFor="isActive"
              className="ml-2 block text-sm text-gray-900"
            >
              Reward is Active
            </label>
          </div>
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
            >
              {isEditing ? 'Update Reward' : 'Create Reward'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RewardFormModal;
