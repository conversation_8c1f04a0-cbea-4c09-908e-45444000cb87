import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, InferQueryOutput } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { CheckCircle } from 'lucide-react';
import { Doc, Id } from '@db/_generated/dataModel';
import { WithLoading } from '../utils/WithLoading';

type RedemptionData = InferQueryOutput<
  typeof api.functions.admin.getRedemptionsForClient
>;

const RedemptionFulfillmentTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'Pending' | 'Fulfilled'>(
    'Pending'
  );
  const [selectedUserId, setSelectedUserId] = useState<
    Id<'users'> | undefined
  >();

  const redemptions = useQuery(api.functions.admin.getRedemptionsForClient, {
    status: statusFilter,
    userId: selectedUserId,
  });

  const foundUsers = useQuery(
    api.functions.admin.findUsersByNameOrEmail,
    searchQuery ? { searchQuery } : 'skip'
  );

  const fulfillRedemption = useMutation(api.functions.admin.fulfillRedemption);

  const handleFulfill = (redemptionId: Id<'userRedemptions'>) => {
    if (
      window.confirm('Are you sure you want to mark this reward as fulfilled?')
    ) {
      toast.promise(fulfillRedemption({ redemptionId }), {
        loading: 'Fulfilling...',
        success: 'Redemption fulfilled!',
        error: 'Failed to fulfill redemption.',
      });
    }
  };

  const usersToShow = useMemo(() => {
    if (searchQuery) return foundUsers || [];
    return [];
  }, [searchQuery, foundUsers]);

  const selectedUser = useMemo(() => {
    return foundUsers?.find((u) => u._id === selectedUserId);
  }, [foundUsers, selectedUserId]);

  return (
    <div>
      <h2 className="mb-4 text-2xl font-bold">Fulfill Redemptions</h2>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* User Search and List */}
        <div className="rounded-lg border bg-white p-4 md:col-span-1">
          <div className="flex items-center justify-between">
            <h3 className="mb-2 text-lg font-semibold">Find Member</h3>
            {selectedUserId && (
              <button
                onClick={() => setSelectedUserId(undefined)}
                className="text-sm text-indigo-600 hover:underline"
              >
                Clear
              </button>
            )}
          </div>
          <input
            type="text"
            placeholder="Search by name or email..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setSelectedUserId(undefined);
            }}
            className="w-full rounded-md border-gray-300"
          />
          <div className="mt-4 max-h-96 space-y-2 overflow-y-auto">
            <WithLoading isPending={foundUsers === undefined && !!searchQuery}>
              {usersToShow.map((user) => (
                <div
                  key={user._id}
                  onClick={() => setSelectedUserId(user._id)}
                  className={`cursor-pointer rounded-md p-2 ${
                    selectedUserId === user._id
                      ? 'bg-indigo-100 ring-2 ring-indigo-500'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <p className="font-semibold">
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-sm text-gray-500">{user.email}</p>
                </div>
              ))}
            </WithLoading>
          </div>
        </div>

        {/* Redemptions List */}
        <div className="rounded-lg border bg-white p-4 md:col-span-2">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              {selectedUser
                ? `Redemptions for ${selectedUser.firstName} ${selectedUser.lastName}`
                : 'All Recent Redemptions'}
            </h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setStatusFilter('Pending')}
                className={`rounded-full px-3 py-1 text-sm ${
                  statusFilter === 'Pending'
                    ? 'bg-yellow-500 text-white'
                    : 'bg-gray-200'
                }`}
              >
                Pending
              </button>
              <button
                onClick={() => setStatusFilter('Fulfilled')}
                className={`rounded-full px-3 py-1 text-sm ${
                  statusFilter === 'Fulfilled'
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200'
                }`}
              >
                Fulfilled
              </button>
            </div>
          </div>

          <div className="space-y-3">
            <WithLoading isPending={redemptions === undefined}>
              {redemptions && redemptions.length === 0 ? (
                <p className="py-8 text-center text-gray-500">
                  No {statusFilter.toLowerCase()} redemptions found.
                </p>
              ) : (
                redemptions &&
                redemptions.map((r) => (
                  <div
                    key={r._id}
                    className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                  >
                    <div>
                      <p className="font-bold text-gray-800">{r.rewardName}</p>
                      {!selectedUserId && (
                        <p className="text-sm text-gray-600">
                          Member: {r.userName}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Redeemed on:{' '}
                        {new Date(r._creationTime).toLocaleDateString()}
                      </p>
                    </div>
                    {r.status === 'Pending' && (
                      <button
                        onClick={() => handleFulfill(r._id)}
                        className="inline-flex items-center justify-center rounded-md border border-transparent bg-green-600 px-3 py-1 text-sm font-medium text-white shadow-sm hover:bg-green-700"
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Fulfill
                      </button>
                    )}
                  </div>
                ))
              )}
            </WithLoading>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RedemptionFulfillmentTab;
