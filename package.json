{"name": "fitness-rewards-platform-monorepo", "private": true, "type": "module", "packageManager": "pnpm@8.15.0", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev --parallel", "dev:db": "pnpm --filter @fitness-rewards/db dev", "dev:web": "pnpm --filter @fitness-rewards/web dev", "build": "turbo build", "test": "turbo test", "lint": "turbo lint", "type-check": "turbo type-check", "clean": "turbo clean && rm -rf node_modules", "format": "prettier --write \"**/*.{ts,tsx,md,json,css,scss}\""}, "devDependencies": {"@types/node": "^20.12.8", "eslint": "^8.57.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "turbo": "^1.13.3", "typescript": "^5.4.5"}}