# FitRewards Platform: System Overview & Mental Model

## Section 1: Application Overview (Product Management Perspective)

**Purpose:**
The Fitness Rewards Platform, "FitRewards," is a comprehensive white-label gamification platform designed for fitness businesses (gyms, studios, corporate wellness programs). It motivates users through a sophisticated rewards system featuring points, tiers, milestones, and customizable rewards. The platform is multi-tenant, allowing different clients to have their own branded experience and custom configurations.

**User Personas:**

1.  **End User (Fitness Enthusiast):**

    - Primary consumer who interacts with the fitness rewards system.
    - Tracks fitness activities (e.g., class attendance) and progress.
    - Earns points, progresses through tiers, and unlocks milestone achievements.
    - Redeems points for rewards (e.g., free smoothies, merchandise).
    - Views a personalized dashboard with progress summaries.

2.  **Client Administrator (Gym/Studio Owner, HR Manager):**

    - Configures their organization's instance of the platform.
    - Sets up branding, defines custom milestones, and manages the rewards catalog.
    - Enables/disables platform features (e.g., tiers, leaderboards).
    - Monitors user engagement and progress.
    - Manages user roles within their organization.

3.  **Staff / Fulfiller (Gym Staff):**

    - Views a list of pending reward redemptions from users.
    - Marks redemptions as "Fulfilled" after providing the user with their reward.
    - Interacts with a specific admin-level interface for fulfillment tasks.

4.  **Platform Administrator (Internal):**
    - Manages multiple client configurations.
    - Handles platform-wide settings and operations.
    - Oversees data seeding and client onboarding.

**Core Features:**

- **User Management & RBAC:** Secure authentication via Clerk, with role-based access control (Admin, Member) synchronized from Clerk Organizations.
- **Gamification Engine:**
  - **Points System:** Core currency earned through activities.
  - **Tier Progression:** Levels that users can achieve based on points or other criteria.
  - **Configurable Milestone Engine:** Admins can define specific, repeatable achievements (e.g., "Attend 10 classes") with custom point rewards.
- **Multi-Tenant Architecture:** Client-specific data, branding, and feature configurations.
- **Activity Tracking:** System logs user activities (e.g., class attendance) which act as triggers for milestones.
- **Rewards Catalog & Fulfillment:** Admins create a catalog of rewards; users redeem rewards with points, and staff fulfill them through a dedicated interface.
- **Customizable Dashboard:** Widget-based layout system for a personalized user experience.
- **Real-time Updates:** Live data synchronization via Convex for a responsive feel.
- **Webhook Integration:** Automated user profile and role synchronization with Clerk.

**High-Level User Flows:**

1.  **New User Onboarding:**

    - User signs up via Clerk on a client-branded page.
    - Clerk webhook triggers a `user.created` event.
    - Convex backend creates a `users` record. The user is assigned to a `client` and given a default role.
    - The user is redirected to their personalized dashboard.

2.  **Milestone Achievement Flow:**

    - User completes a fitness activity (e.g., attends a class).
    - The system logs this in the `activities` table.
    - A Convex function evaluates this new activity against all active `milestones` for that client.
    - If a milestone's conditions are met (e.g., `countThreshold` reached), the user is awarded points and a `userMilestoneProgress` record is created.
    - The user's dashboard updates in real-time to show new points and achieved milestone.

3.  **Reward Redemption & Fulfillment Flow:**

    - User browses the `RewardsPage` and spends points on a reward.
    - A `userRedemptions` record is created with `Pending` status.
    - The Staff/Fulfiller views a list of pending redemptions in the `RedemptionFulfillmentTab` on the admin page.
    - Staff provides the physical reward to the user (e.g., a smoothie).
    - Staff marks the redemption as `Fulfilled` in the UI.
    - The `userRedemptions` record is updated, and the user's history reflects the completed redemption.

4.  **Admin Configuration Flow:**
    - Client Admin accesses the `AdminPage`.
    - Navigates to the `MilestonesManagementTab` or `RewardsManagementTab`.
    - Creates/updates milestones or rewards using dedicated forms (`MilestoneFormModal`, `RewardFormModal`).
    - Changes are saved to the `milestones` and `rewards` tables and are immediately active for users of that client.

## Section 2: Software Architecture (Architect Perspective)

**Architectural Style:**
The application implements a **modern cloud-native architecture** using a **Monorepo** structure. It leverages a **Backend-as-a-Service (BaaS)** platform (Convex) and a **decoupled Service Layer** for core business logic.

- **Monorepo Structure:** Managed by pnpm workspaces and Turbo for efficient, cohesive development.
- **Frontend:** A Single Page Application (SPA) built with React, Vite, and TypeScript.
- **Backend (BaaS):** Serverless architecture using Convex, which provides the real-time database, serverless functions (queries, mutations, actions), and authentication integration.
- **Service Layer (`packages/core`):** A dedicated package containing pure, decoupled business logic (e.g., `MilestoneService`), making the domain logic portable, reusable, and independently testable.
- **Authentication:** Fully managed by an external service (Clerk), with user/role synchronization via webhooks.
- **Multi-Tenancy:** Achieved through database-level tenant isolation (`clientId` fields) and dynamic, client-specific configurations.

**Key Components/Modules:**

1.  **Frontend Application (`apps/web`):**

    - The main user-facing React SPA. Contains all UI components, pages, hooks, and routing logic.
    - Integrates with Convex via `convex/react-clerk` for data fetching/subscriptions and with Clerk via `@clerk/clerk-react` for UI components.

2.  **Backend Infrastructure (`packages/db`):**

    - The heart of the Convex application.
    - `convex/schema.ts`: Defines the entire database schema, tables, and indexes.
    - `convex/functions/`: Contains all serverless functions (mutations, queries) that interact with the database and core services.
    - `convex/http.ts`: Handles incoming webhooks from Clerk for user synchronization.
    - `convex/auth.config.ts`: Configures the authentication provider (Clerk).

3.  **Core Business Logic (`packages/core`):**

    - A separate, framework-agnostic package.
    - Contains pure TypeScript functions encapsulating key business rules (e.g., `MilestoneService.ts`, `TierService.ts`).
    - Consumed by the Convex functions in `packages/db` to ensure a separation of concerns.

4.  **Shared Libraries & Assets:**
    - `packages/shared`: Common types and utilities.
    - `clients/`: Contains tenant-specific configurations, assets, and documentation (e.g., style guides for "The Handle Bar").

**Data Models & Relationships:**
The database schema is defined in `packages/db/convex/schema.ts` and is significantly more detailed than a simple user/client model. Key tables include:

- `users`: Stores user profiles, linked to a `clerkUserId` and a `clientId`. Includes points, tier, and role.
- `clients`: Represents the tenant organizations.
- `clientConfiguration`: Holds client-specific branding, feature flags, and dashboard layouts.
- `activities`: A log of all significant user actions (e.g., `class_attendance`). This is the event source for the gamification engine.
- `milestones`: Configurable rules that trigger rewards based on activity patterns (e.g., attend 10 classes).
- `rewards`: The catalog of items users can purchase with points.
- `userRedemptions`: A join table tracking which users have redeemed which rewards, including a `status` for the fulfillment workflow.
- `userMilestoneProgress`: Tracks which milestones a user has achieved.
- `tierProgressions`: Logs the history of a user's advancement through tiers.

**Diagrams (Mermaid.js):**

**System Architecture Overview:**

```mermaid
graph TD
    subgraph "User Layer"
        User[End Users]
        Admin[Client Admins & Staff]
    end

    subgraph "Frontend (Vercel)"
        WebApp[React SPA<br/>apps/web]
        WebApp --> ClerkReact[Clerk React SDK]
        WebApp --> ConvexReact[Convex React SDK]
    end

    subgraph "Authentication (Clerk Cloud)"
        ClerkAuth[Clerk Service]
        ClerkAuth --> Webhooks[User & Org Webhooks]
    end

    subgraph "Backend (Convex Cloud)"
        ConvexAPI[Convex API Layer]
        ConvexDB[(Convex Database)]
        subgraph "Convex Runtime"
            Functions[Serverless Functions<br/>packages/db/convex/functions]
            WebhookHandler[HTTP Action<br/>packages/db/convex/http.ts]
            CoreServices[Core Service Logic<br/>packages/core]
        end

        ConvexAPI --> Functions
        WebhookHandler --> Functions
        Functions --> CoreServices
        Functions --> ConvexDB
    end

    User --> WebApp
    Admin --> WebApp
    ClerkReact --> ClerkAuth
    ConvexReact --> ConvexAPI
    Webhooks --> WebhookHandler
```

**User Authentication & Role Synchronization Flow:**

```mermaid
sequenceDiagram
    participant User
    participant Frontend as React SPA
    participant Clerk
    participant Webhook as Convex HTTP Handler
    participant Functions as Convex Functions

    User->>Frontend: Sign In / Sign Up
    Frontend->>Clerk: Authenticate User
    Clerk-->>Frontend: Return JWT

    alt User Creation/Update
        Clerk->>Webhook: POST /webhooks/clerk (user.created)
        Webhook->>Functions: internalStoreOrUpdateUser
    else Role Update
        Clerk->>Webhook: POST /webhooks/clerk (organizationMembership.created)
        Webhook->>Functions: internalSetUserRole
    end

    Frontend->>Functions: API Call with JWT
    Functions->>ConvexDB: Query Data
    ConvexDB-->>Functions: Return Data
    Functions-->>Frontend: Return Data to UI
```

**Data Model Relationships:**

```mermaid
graph TD
    Users -- "Belongs to" --> Clients
    ClientConfiguration -- "Configures" --> Clients
    Activities -- "Performed by" --> Users
    Activities -- "Belongs to" --> Clients
    Milestones -- "Configured for" --> Clients
    Rewards -- "Offered by" --> Clients
    UserRedemptions -- "Made by" --> Users
    UserRedemptions -- "Fulfills" --> Rewards
    UserMilestoneProgress -- "Tracks for" --> Users
    UserMilestoneProgress -- "Corresponds to" --> Milestones
    TierProgressions -- "Logs for" --> Users
```

## Section 3: Development Details (Developer Perspective)

**Technology Stack:**

- **Core:** TypeScript, Node.js
- **Monorepo:** pnpm workspaces, Turbo (`^1.13.3`)
- **Frontend:** React (`^18.3.1`), Vite (`^5.2.11`), React Router (`^6.23.1`), Framer Motion, Lucide Icons
- **Styling:** Tailwind CSS (`^3.4.3`) with PostCSS/Autoprefixer
- **Backend:** Convex (`^1.24.8`)
- **Authentication:** Clerk (`^5.0.0`)
- **Webhook Verification:** Svix (`^1.22.0`)
- **Data Validation:** Zod (`^3.23.8`) (used in `packages/db`)
- **Code Quality:** ESLint, Prettier, TypeScript (strict)

**Project Structure Deep Dive:**

```
fitness-rewards-platform/
├── 📁 apps/
│   └── 📁 web/                    # Main React frontend SPA
│       ├── 📄 vite.config.ts      # Build configuration
│       └── 📁 src/
│           ├── 📄 main.tsx         # App entry point, providers (Clerk, Convex)
│           ├── 📄 AppRoutes.tsx    # All application routes
│           ├── 📁 pages/           # Top-level page components
│           └── 📁 components/      # Reusable UI components (including admin)
├── 📁 packages/
│   ├── 📁 core/                   # Decoupled business logic
│   │   └── 📁 src/services/
│   │       ├── MilestoneService.ts # Logic for evaluating milestone achievement
│   │       └── TierService.ts      # Logic for tier progression
│   ├── 📁 db/                     # Convex backend application
│   │   └── 📁 convex/
│   │       ├── 📄 schema.ts       # THE single source of truth for data models
│   │       ├── 📄 http.ts         # Clerk webhook handler
│   │       ├── 📄 auth.config.ts  # Clerk auth provider config
│   │       └── 📁 functions/      # All DB queries, mutations, and actions
│   ├── 📁 shared/                 # (Future) Shared types and utilities
│   └── 📁 emails/                 # (Future) Email service integration
├── 📁 clients/                    # Tenant-specific assets and configuration
│   └── 📁 The Handle Bar/
│       └── 📁 docs/
│           └── THE_HANDLE_BAR_STYLE_GUIDE.md
└── 📄 turbo.json              # Monorepo build pipeline configuration
```

**Key Development Patterns:**

1.  **Backend-as-a-Service (BaaS) Integration:** The application offloads all backend infrastructure management to Convex. Developers define a schema and write serverless functions, and Convex handles the database, API endpoints, real-time updates, and scaling.

2.  **Decoupled Business Logic (Service Layer):** Core domain logic is isolated in `packages/core`. Convex functions in `packages/db` import and use these services. This separates the "what" (business rules) from the "how" (database interaction, API exposure), improving testability and maintainability.

3.  **Webhook-Driven User Sync:** The system relies entirely on webhooks from Clerk to keep its `users` table synchronized. This is an event-driven approach that decouples the app from Clerk's internal state. The `http.ts` handler is the single entry point for this data.

4.  **Official SDKs for Integration:** The frontend uses the official `ConvexProviderWithClerk` and `@clerk/clerk-react` libraries, ensuring a well-supported and streamlined integration path, rather than relying on custom-built solutions.

5.  **Configuration-Driven UI:** Features and branding are controlled by the `clientConfiguration` table. The frontend queries this configuration and conditionally renders UI elements (e.g., showing a `TierWidget` only if `features.tiersEnabled` is true), allowing for powerful per-client customization without code changes.

**Authentication & Authorization:**

- **Authentication:** Handled by Clerk. JWTs issued by Clerk are passed with every Convex API call. Convex automatically validates these tokens on the backend.
- **Authorization (RBAC):** A user's role (`admin` or `member`) is synced from their Clerk Organization membership via webhook into the `users.role` field. Convex functions and frontend components can then check this role to authorize access to specific data or functionality (e.g., the entire `/admin` page).

**Build Process & DevOps:**

- **Monorepo Tooling:** `turbo` orchestrates the build, dev, and linting processes across the entire repository.
- **Development:** `pnpm dev` starts the Vite dev server for the frontend and the Convex dev environment for the backend in parallel.
- **Dependencies:** Managed with `pnpm workspaces`.
- **Deployment:** The frontend (`apps/web`) is configured for deployment on Vercel. The backend (`packages/db`) is deployed to the Convex cloud via the `convex deploy` command. Environment variables are managed in the respective hosting platforms.
- **Testing:** The `turbo.json` pipeline is configured for a `test` command, but no tests are currently implemented. The `packages/core` service layer is structured to be easily unit-tested.

**Comments:**

- **JSDOCS:** Prefer JSDOC style comments(https://jsdoc.app/) along with typescript. Replace old comments with JSDOC style comments where appropriate.
