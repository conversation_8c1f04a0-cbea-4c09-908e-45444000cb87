# Documentation Directory

This directory contains all product and technical documentation for the Fitness Rewards Platform.

## Naming Convention

### Architecture Documents

- **Prefix**: `ARCH_`
- **Format**: `ARCH_[descriptive-name].md`
- **Purpose**: System overviews, technical architecture, and platform design documentation

### Product Requirements Documents (PRDs)

- **Prefix**: `PRD_`
- **Format**: `PRD_[feature-name].md`
- **Purpose**: Feature specifications, requirements, and development guidelines

## Current Documents

### Architecture

- `ARCH_system-overview.md` - Comprehensive platform architecture and system design

### Product Requirements

- `PRD_admin-dashboard.md` - Client administrator dashboard and rewards management
- `PRD_class-attendance.md` - Class attendance logging and milestone tracking
- `PRD_milestone-management.md` - Client-facing milestone management interface
- `PRD_rewards-catalog.md` - Rewards catalog and redemption system
- `PRD_staff-fulfillment.md` - In-person reward fulfillment for front-desk staff
- `PRD_tier-system.md` - User tier progression and status system

## Document Standards

- Use kebab-case for file names (lowercase with hyphens)
- Include version, author, and status in document headers
- Maintain consistent formatting across all PRDs
- Update this README when adding new documents
